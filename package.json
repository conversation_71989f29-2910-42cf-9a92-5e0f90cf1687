{"name": "vite_react_shadcn_ts", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:dev": "vite build --mode development", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@hookform/resolvers": "5.1.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@tanstack/react-query": "^5.80.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "4.1.0", "embla-carousel-react": "^8.6.0", "input-otp": "^1.4.2", "lucide-react": "^0.516.0", "next-themes": "^0.4.6", "react": "19.1.0", "react-day-picker": "9.7.0", "react-dom": "19.1.0", "react-hook-form": "^7.58.0", "react-resizable-panels": "3.0.3", "react-router-dom": "7.6.2", "recharts": "^2.15.3", "sonner": "2.0.5", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "vaul": "1.1.2", "zod": "^3.25.67"}, "devDependencies": {"@eslint/js": "^9.29.0", "@tailwindcss/typography": "^0.5.16", "@types/node": "24.0.3", "@types/react": "19.1.8", "@types/react-dom": "19.1.6", "@vitejs/plugin-react-swc": "^3.10.2", "autoprefixer": "^10.4.21", "eslint": "^9.29.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "16.2.0", "lovable-tagger": "^1.1.8", "postcss": "^8.5.6", "tailwindcss": "^3.4.11", "typescript": "^5.8.3", "typescript-eslint": "^8.34.1", "vite": "6.3.5"}}