
import { Card, CardContent } from "@/components/ui/card";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import { Camera } from "lucide-react";

const galleryImages = [
  {
    url: "https://images.unsplash.com/photo-1649972904349-6e44c42644a7?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
    title: "Atendimento Personalizado",
    description: "Cuidado individual e humanizado"
  },
  {
    url: "https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
    title: "Ambiente Acolhedor",
    description: "Espaço preparado para seu bem-estar"
  },
  {
    url: "https://images.unsplash.com/photo-1721322800607-8c38375eef04?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
    title: "Consultório Moderno",
    description: "Equipamentos de última geração"
  },
  {
    url: "https://images.unsplash.com/photo-1465146344425-f00d5f5c8f07?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
    title: "Ambiente Natural",
    description: "Conexão com a natureza"
  },
  {
    url: "https://images.unsplash.com/photo-1500673922987-e212871fec22?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
    title: "Tranquilidade",
    description: "Espaço de paz e relaxamento"
  }
];

const GallerySection = () => {
  return (
    <section className="py-12 md:py-20 px-4 bg-gradient-to-br from-purple-50 to-pink-50">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-12 md:mb-16 animate-fade-in">
          <div className="flex items-center justify-center space-x-2 mb-4">
            <Camera className="h-6 w-6 md:h-8 md:w-8 text-pink-600" />
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-800">
              Galeria de <span className="text-pink-600">Momentos</span>
            </h2>
          </div>
          <p className="text-lg md:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Conheça nosso espaço e veja como criamos um ambiente acolhedor para seu tratamento
          </p>
        </div>

        <div className="animate-fade-in">
          <Carousel className="w-full max-w-5xl mx-auto">
            <CarouselContent className="-ml-2 md:-ml-4">
              {galleryImages.map((image, index) => (
                <CarouselItem key={index} className="pl-2 md:pl-4 basis-full sm:basis-1/2 lg:basis-1/3">
                  <div className="p-1">
                    <Card className="group overflow-hidden border-none shadow-lg hover:shadow-2xl transition-all duration-300 hover:-translate-y-2">
                      <CardContent className="p-0">
                        <div className="relative">
                          <img 
                            src={image.url}
                            alt={image.title}
                            className="w-full h-48 md:h-64 object-cover group-hover:scale-110 transition-transform duration-300"
                          />
                          <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                          <div className="absolute bottom-0 left-0 right-0 p-3 md:p-4 text-white transform translate-y-full group-hover:translate-y-0 transition-transform duration-300">
                            <h3 className="font-bold text-base md:text-lg mb-1">{image.title}</h3>
                            <p className="text-xs md:text-sm text-gray-200">{image.description}</p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                </CarouselItem>
              ))}
            </CarouselContent>
            <CarouselPrevious className="hidden md:flex left-2 md:left-4" />
            <CarouselNext className="hidden md:flex right-2 md:right-4" />
          </Carousel>
        </div>
      </div>
    </section>
  );
};

export default GallerySection;
