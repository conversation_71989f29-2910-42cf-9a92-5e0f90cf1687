
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Heart, Activity, Flower, CheckCircle, ArrowRight, Clock, Users } from "lucide-react";

const treatmentDetails = [
  {
    icon: Heart,
    title: "Fisioterapia Integrativa Pélvica",
    shortDesc: "Tratamento especializado para disfunções do assoalho pélvico",
    benefits: [
      "Fortalecimento do assoalho pélvico",
      "Melhoria da incontinência urinária",
      "Tratamento de disfunções sexuais",
      "Alívio de dores pélvicas",
      "Preparação pré e pós-parto"
    ],
    duration: "60 minutos",
    frequency: "1-2x por semana",
    color: "from-pink-500 to-rose-500",
    bgColor: "bg-pink-50"
  },
  {
    icon: Activity,
    title: "Pilates Terapêutico",
    shortDesc: "Fortalecimento do core e estabilização corporal",
    benefits: [
      "Fortalecimento do core profundo",
      "Melhoria da postura corporal",
      "Redução de dores nas costas",
      "Aumento da flexibilidade",
      "Consciência corporal"
    ],
    duration: "50 minutos",
    frequency: "2-3x por semana",
    color: "from-purple-500 to-indigo-500",
    bgColor: "bg-purple-50"
  },
  {
    icon: Flower,
    title: "Yoga Hormonal",
    shortDesc: "Equilíbrio natural dos hormônios femininos",
    benefits: [
      "Regulação do ciclo menstrual",
      "Alívio dos sintomas da menopausa",
      "Redução do estresse e ansiedade",
      "Melhoria do sono",
      "Aumento da energia vital"
    ],
    duration: "75 minutos",
    frequency: "2x por semana",
    color: "from-indigo-500 to-blue-500",
    bgColor: "bg-indigo-50"
  }
];

const TreatmentExplanationSection = () => {
  return (
    <section className="py-16 md:py-24 px-4 bg-white">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-16 animate-fade-in">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-6">
            Como Funcionam Nossos <span className="text-pink-600">Tratamentos</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Entenda em detalhes como cada tratamento pode transformar sua qualidade de vida 
            através de técnicas comprovadas e cuidado personalizado.
          </p>
        </div>

        <div className="grid lg:grid-cols-3 gap-8 mb-16">
          {treatmentDetails.map((treatment, index) => (
            <Card 
              key={index}
              className={`${treatment.bgColor} border-none shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-2 animate-fade-in`}
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              <CardContent className="p-8">
                <div className="text-center mb-6">
                  <div className={`w-16 h-16 mx-auto rounded-full bg-gradient-to-br ${treatment.color} flex items-center justify-center mb-4`}>
                    <treatment.icon className="h-8 w-8 text-white" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-800 mb-2">{treatment.title}</h3>
                  <p className="text-gray-600 text-sm">{treatment.shortDesc}</p>
                </div>

                <div className="space-y-4 mb-6">
                  <h4 className="font-semibold text-gray-800 text-center">Benefícios:</h4>
                  <ul className="space-y-2">
                    {treatment.benefits.slice(0, 3).map((benefit, benefitIndex) => (
                      <li key={benefitIndex} className="flex items-start space-x-2 text-sm">
                        <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                        <span className="text-gray-600">{benefit}</span>
                      </li>
                    ))}
                  </ul>
                </div>

                <div className="space-y-3 mb-6 p-4 bg-white/60 rounded-lg">
                  <div className="flex items-center justify-between text-sm">
                    <div className="flex items-center space-x-2">
                      <Clock className="h-4 w-4 text-gray-500" />
                      <span className="text-gray-600">Duração:</span>
                    </div>
                    <span className="font-semibold text-gray-800">{treatment.duration}</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <div className="flex items-center space-x-2">
                      <Users className="h-4 w-4 text-gray-500" />
                      <span className="text-gray-600">Frequência:</span>
                    </div>
                    <span className="font-semibold text-gray-800">{treatment.frequency}</span>
                  </div>
                </div>

                <Button 
                  className={`w-full bg-gradient-to-r ${treatment.color} hover:opacity-90 text-white py-3 rounded-lg font-semibold group transition-all duration-300 shadow-md hover:shadow-lg`}
                  onClick={() => document.getElementById('contact')?.scrollIntoView({ behavior: 'smooth' })}
                >
                  Agendar Consulta
                  <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="text-center">
          <div className="inline-flex items-center space-x-4 bg-gray-50 px-8 py-4 rounded-full">
            <div className="text-center">
              <div className="text-2xl font-bold text-pink-600">500+</div>
              <div className="text-sm text-gray-600">Pacientes Atendidas</div>
            </div>
            <div className="w-px h-8 bg-gray-300"></div>
            <div className="text-center">
              <div className="text-2xl font-bold text-pink-600">98%</div>
              <div className="text-sm text-gray-600">Satisfação</div>
            </div>
            <div className="w-px h-8 bg-gray-300"></div>
            <div className="text-center">
              <div className="text-2xl font-bold text-pink-600">8+</div>
              <div className="text-sm text-gray-600">Anos de Experiência</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default TreatmentExplanationSection;
