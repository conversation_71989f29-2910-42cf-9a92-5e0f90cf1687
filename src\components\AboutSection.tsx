
import { Card, CardContent } from "@/components/ui/card";
import { Award, Heart, Users, Clock } from "lucide-react";

const stats = [
  { icon: Users, value: "500+", label: "Pacientes Atendidas" },
  { icon: Clock, value: "8+", label: "Anos de Experiência" },
  { icon: Heart, value: "98%", label: "Satisfação" },
  { icon: Award, value: "15+", label: "Especializações" }
];

const AboutSection = () => {
  return (
    <section className="py-12 md:py-20 px-4 bg-gradient-to-r from-pink-50 to-purple-50">
      <div className="max-w-6xl mx-auto">
        <div className="grid lg:grid-cols-2 gap-8 md:gap-16 items-center">
          <div className="space-y-6 md:space-y-8 animate-fade-in order-2 lg:order-1">
            <div>
              <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-800 mb-4 md:mb-6 text-center lg:text-left">
                Sobre <span className="text-pink-600"><PERSON><PERSON>laj</span>
              </h2>
              <div className="space-y-4 text-base md:text-lg text-gray-600 leading-relaxed">
                <p>
                  Fisioterapeuta especializada em saúde pélvica com formação em terapias integrativas. 
                  Minha missão é proporcionar cuidado humanizado e tratamentos eficazes para melhorar 
                  a qualidade de vida das mulheres.
                </p>
                <p>
                  Com mais de 8 anos de experiência, combino técnicas modernas de fisioterapia com 
                  abordagens holísticas como yoga hormonal, aromaterapia e acupuntura, oferecendo 
                  um tratamento completo e personalizado.
                </p>
                <p>
                  Acredito que cada mulher merece se sentir plena e confiante em seu próprio corpo. 
                  Trabalho com dedicação para criar um ambiente acolhedor onde você possa se sentir 
                  à vontade para discutir suas necessidades e objetivos.
                </p>
              </div>
            </div>
            
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 md:gap-6">
              {stats.map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="w-10 h-10 md:w-12 md:h-12 bg-pink-600 rounded-full flex items-center justify-center mx-auto mb-2">
                    <stat.icon className="h-5 w-5 md:h-6 md:w-6 text-white" />
                  </div>
                  <div className="text-xl md:text-2xl font-bold text-pink-600">{stat.value}</div>
                  <div className="text-xs md:text-sm text-gray-600">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
          
          <div className="relative animate-fade-in order-1 lg:order-2">
            <div className="relative z-10">
              <img 
                src="https://images.unsplash.com/photo-1649972904349-6e44c42644a7?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                alt="Mariah Blaj - Fisioterapeuta"
                className="w-full h-80 md:h-96 object-cover rounded-3xl shadow-2xl"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-pink-600/20 to-transparent rounded-3xl" />
            </div>
            <div className="absolute -top-3 md:-top-6 -right-3 md:-right-6 w-16 h-16 md:w-32 md:h-32 bg-gradient-to-br from-pink-400 to-purple-400 rounded-full opacity-20 animate-pulse" />
            <div className="absolute -bottom-3 md:-bottom-6 -left-3 md:-left-6 w-12 h-12 md:w-24 md:h-24 bg-gradient-to-br from-purple-400 to-indigo-400 rounded-full opacity-20 animate-pulse delay-1000" />
          </div>
        </div>
      </div>
    </section>
  );
};

export default AboutSection;
