
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent } from "@/components/ui/card";
import { Phone, Mail, MapPin, Clock, Instagram, MessageCircle } from "lucide-react";

const ContactSection = () => {
  return (
    <section id="contact" className="py-20 px-4">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-16 animate-fade-in">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-4">
            Agende Sua <span className="text-pink-600">Consulta</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Estou aqui para ajudar você a alcançar seu bem-estar. Entre em contato e vamos conversar sobre 
            suas necessidades.
          </p>
        </div>
        
        <div className="grid lg:grid-cols-2 gap-12">
          <div className="space-y-8 animate-fade-in">
            <div className="space-y-6">
              <div className="flex items-start space-x-4">
                <div className="w-12 h-12 bg-pink-600 rounded-full flex items-center justify-center flex-shrink-0">
                  <MapPin className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h3 className="text-xl font-bold text-gray-800 mb-2">Localização</h3>
                  <p className="text-gray-600">São Paulo, SP</p>
                  <p className="text-gray-600">Consultório em região central</p>
                </div>
              </div>
              
              <div className="flex items-start space-x-4">
                <div className="w-12 h-12 bg-pink-600 rounded-full flex items-center justify-center flex-shrink-0">
                  <Clock className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h3 className="text-xl font-bold text-gray-800 mb-2">Horários</h3>
                  <p className="text-gray-600">Segunda a Sexta: 8h às 18h</p>
                  <p className="text-gray-600">Sábado: 8h às 14h</p>
                </div>
              </div>
              
              <div className="flex items-start space-x-4">
                <div className="w-12 h-12 bg-pink-600 rounded-full flex items-center justify-center flex-shrink-0">
                  <Phone className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h3 className="text-xl font-bold text-gray-800 mb-2">Contato</h3>
                  <p className="text-gray-600">(11) 99999-9999</p>
                  <p className="text-gray-600"><EMAIL></p>
                </div>
              </div>
            </div>
            
            <div className="flex space-x-4">
              <Button 
                className="bg-green-600 hover:bg-green-700 text-white px-8 py-4 rounded-full font-semibold group transition-all duration-300 hover:scale-105"
                onClick={() => window.open('https://wa.me/5511999999999', '_blank')}
              >
                <MessageCircle className="mr-2 h-5 w-5" />
                WhatsApp
              </Button>
              <Button 
                variant="outline"
                className="border-pink-600 text-pink-600 hover:bg-pink-50 px-8 py-4 rounded-full font-semibold"
                onClick={() => window.open('https://instagram.com/mariahblaj', '_blank')}
              >
                <Instagram className="mr-2 h-5 w-5" />
                Instagram
              </Button>
            </div>
          </div>
          
          <Card className="animate-fade-in bg-white/80 backdrop-blur-sm shadow-2xl border-none">
            <CardContent className="p-8">
              <h3 className="text-2xl font-bold text-gray-800 mb-6">Solicite Informações</h3>
              <form className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Nome</label>
                  <input 
                    type="text" 
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-all"
                    placeholder="Seu nome completo"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Telefone</label>
                  <input 
                    type="tel" 
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-all"
                    placeholder="(11) 99999-9999"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Tratamento de Interesse</label>
                  <select className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-all">
                    <option value="">Selecione uma opção</option>
                    <option value="fisioterapia">Fisioterapia Pélvica</option>
                    <option value="pilates">Pilates Terapêutico</option>
                    <option value="yoga">Yoga Hormonal</option>
                    <option value="aromaterapia">Aromaterapia</option>
                    <option value="acupuntura">Acupuntura</option>
                    <option value="avaliacao">Avaliação Completa</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Mensagem</label>
                  <textarea 
                    rows={4}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent transition-all"
                    placeholder="Conte um pouco sobre suas necessidades..."
                  />
                </div>
                <Button 
                  type="submit"
                  className="w-full bg-pink-600 hover:bg-pink-700 text-white py-4 rounded-lg text-lg font-semibold transition-all duration-300 hover:scale-105"
                >
                  Enviar Solicitação
                </Button>
              </form>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
};

export default ContactSection;
