
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { 
  MessageCircle, 
  Calendar, 
  Instagram, 
  Mail, 
  MapPin, 
  Phone, 
  FileText, 
  Users, 
  Heart,
  ExternalLink,
  Clock,
  Sparkles
} from "lucide-react";
import Header from "@/components/Header";

const links = [
  {
    title: "Agendar Consulta",
    description: "WhatsApp direto comigo",
    icon: MessageCircle,
    url: "https://wa.me/5511999999999",
    color: "from-green-500 to-green-600",
    featured: true
  },
  {
    title: "Calendário Online",
    description: "Veja horários disponíveis",
    icon: Calendar,
    url: "https://calendly.com/mariahblaj",
    color: "from-blue-500 to-blue-600"
  },
  {
    title: "Instagram",
    description: "Dicas diárias de saúde pélvica",
    icon: Instagram,
    url: "https://instagram.com/mariahblaj",
    color: "from-pink-500 to-purple-500"
  },
  {
    title: "E-book Gratuito",
    description: "<PERSON><PERSON>a completo de exercícios",
    icon: FileText,
    url: "https://mariahblaj.com.br/ebook",
    color: "from-indigo-500 to-purple-500"
  },
  {
    title: "Grupo VIP WhatsApp",
    description: "Comunidade de mulheres",
    icon: Users,
    url: "https://chat.whatsapp.com/mariahblaj",
    color: "from-teal-500 to-green-500"
  },
  {
    title: "Localização",
    description: "Como chegar ao consultório",
    icon: MapPin,
    url: "https://maps.google.com/search/fisioterapia+pelv",
    color: "from-red-500 to-pink-500"
  },
  {
    title: "Contato Email",
    description: "<EMAIL>",
    icon: Mail,
    url: "mailto:<EMAIL>",
    color: "from-gray-500 to-gray-600"
  },
  {
    title: "Telefone",
    description: "(11) 99999-9999",
    icon: Phone,
    url: "tel:+5511999999999",
    color: "from-orange-500 to-red-500"
  }
];

const LinksPage = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-pink-50 via-white to-purple-50">
      <Header />
      
      <main className="pt-24 md:pt-28 pb-16 px-4">
        <div className="max-w-lg mx-auto">
          {/* Profile Section */}
          <div className="text-center mb-12 animate-fade-in">
            <div className="relative inline-block mb-8">
              <div className="w-32 h-32 md:w-40 md:h-40 mx-auto rounded-full bg-gradient-to-br from-pink-400 to-purple-500 p-1 shadow-2xl">
                <img 
                  src="https://images.unsplash.com/photo-1649972904349-6e44c42644a7?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&q=80"
                  alt="Mariah Blaj"
                  className="w-full h-full rounded-full object-cover"
                />
              </div>
              <div className="absolute -bottom-2 -right-2 w-10 h-10 bg-gradient-to-br from-green-400 to-green-500 rounded-full flex items-center justify-center border-4 border-white shadow-lg">
                <Heart className="h-5 w-5 text-white fill-current" />
              </div>
              <div className="absolute -top-2 -left-2 w-8 h-8 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full flex items-center justify-center border-2 border-white shadow-md animate-pulse">
                <Sparkles className="h-4 w-4 text-white" />
              </div>
            </div>
            
            <h1 className="text-3xl md:text-4xl font-bold text-gray-800 mb-3">
              Mariah Blaj
            </h1>
            <p className="text-lg md:text-xl text-gray-600 mb-4 font-medium">
              Fisioterapeuta Pélvica
            </p>
            <p className="text-gray-500 max-w-sm mx-auto leading-relaxed">
              Especialista em saúde pélvica feminina, pilates terapêutico e terapias integrativas
            </p>
            
            {/* Quick Stats */}
            <div className="flex justify-center space-x-8 mt-8 bg-white/60 backdrop-blur-sm rounded-2xl p-6 shadow-lg">
              <div className="text-center">
                <div className="text-xl font-bold text-pink-600">500+</div>
                <div className="text-xs text-gray-500">Pacientes</div>
              </div>
              <div className="text-center">
                <div className="text-xl font-bold text-pink-600">8+</div>
                <div className="text-xs text-gray-500">Anos</div>
              </div>
              <div className="text-center">
                <div className="text-xl font-bold text-pink-600">98%</div>
                <div className="text-xs text-gray-500">Satisfação</div>
              </div>
            </div>
          </div>

          {/* Links Section */}
          <div className="space-y-4">
            {links.map((link, index) => (
              <Card 
                key={index}
                className={`group hover:shadow-2xl transition-all duration-300 hover:-translate-y-2 border-none overflow-hidden animate-fade-in ${
                  link.featured ? 'ring-2 ring-pink-300 shadow-lg' : 'shadow-md'
                }`}
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <CardContent className="p-0">
                  <a 
                    href={link.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="block"
                  >
                    <div className={`h-2 bg-gradient-to-r ${link.color} group-hover:h-3 transition-all duration-300`} />
                    <div className="p-6 bg-white/80 backdrop-blur-sm">
                      <div className="flex items-center space-x-4">
                        <div className={`w-14 h-14 rounded-2xl bg-gradient-to-br ${link.color} flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg`}>
                          <link.icon className="h-7 w-7 text-white" />
                        </div>
                        <div className="flex-1 min-w-0">
                          <h3 className="text-lg font-bold text-gray-800 group-hover:text-pink-600 transition-colors mb-1">
                            {link.title}
                          </h3>
                          <p className="text-sm text-gray-500">
                            {link.description}
                          </p>
                        </div>
                        <div className="flex items-center space-x-2">
                          {link.featured && (
                            <div className="px-3 py-1 bg-gradient-to-r from-pink-500 to-purple-500 text-white text-xs font-semibold rounded-full">
                              DESTAQUE
                            </div>
                          )}
                          <ExternalLink className="h-5 w-5 text-gray-400 group-hover:text-pink-600 transition-colors opacity-0 group-hover:opacity-100" />
                        </div>
                      </div>
                    </div>
                  </a>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Footer Info */}
          <div className="mt-12 text-center space-y-4 animate-fade-in bg-white/60 backdrop-blur-sm rounded-2xl p-6 shadow-lg" style={{ animationDelay: '0.8s' }}>
            <div className="flex items-center justify-center space-x-3 text-sm text-gray-600">
              <Clock className="h-4 w-4 text-pink-500" />
              <span className="font-medium">Segunda a Sexta: 8h às 18h | Sábado: 8h às 14h</span>
            </div>
            <div className="flex items-center justify-center space-x-3 text-sm text-gray-600">
              <MapPin className="h-4 w-4 text-pink-500" />
              <span className="font-medium">São Paulo, SP</span>
            </div>
            <p className="text-xs text-gray-400 pt-4 border-t border-gray-200">
              © 2024 Mariah Blaj - Todos os direitos reservados
            </p>
          </div>
        </div>
      </main>
    </div>
  );
};

export default LinksPage;
