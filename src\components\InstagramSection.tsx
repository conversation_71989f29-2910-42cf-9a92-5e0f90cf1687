
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Instagram, ExternalLink, Heart } from "lucide-react";

const instagramPosts = [
  {
    id: 1,
    image: "https://images.unsplash.com/photo-1544367567-0f2fcb009e0b?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80",
    caption: "Exercícios de fortalecimento do assoalho pélvico 💪",
    likes: 156,
    url: "https://instagram.com/p/example1"
  },
  {
    id: 2,
    image: "https://images.unsplash.com/photo-1506126613408-eca07ce68773?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80",
    caption: "Dicas de yoga hormonal para equilibrio natural ✨",
    likes: 243,
    url: "https://instagram.com/p/example2"
  },
  {
    id: 3,
    image: "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80",
    caption: "A importância do autocuidado feminino 🌸",
    likes: 189,
    url: "https://instagram.com/p/example3"
  },
  {
    id: 4,
    image: "https://images.unsplash.com/photo-1506629905216-f1f654a2c1db?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80",
    caption: "Pilates terapêutico para fortalecimento do core 🎯",
    likes: 298,
    url: "https://instagram.com/p/example4"
  },
  {
    id: 5,
    image: "https://images.unsplash.com/photo-1559827260-dc66d52bef19?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80",
    caption: "Aromaterapia: óleos essenciais para relaxamento 🌿",
    likes: 167,
    url: "https://instagram.com/p/example5"
  },
  {
    id: 6,
    image: "https://images.unsplash.com/photo-1576086213369-97a306d36557?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80",
    caption: "Acupuntura para equilíbrio energético ⚡",
    likes: 134,
    url: "https://instagram.com/p/example6"
  }
];

const InstagramSection = () => {
  return (
    <section className="py-12 md:py-20 px-4 bg-gradient-to-r from-purple-50 to-pink-50">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-12 md:mb-16 animate-fade-in">
          <div className="flex items-center justify-center space-x-3 mb-4">
            <Instagram className="h-8 w-8 md:h-10 md:w-10 text-pink-600" />
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-800">
              Siga no <span className="text-pink-600">Instagram</span>
            </h2>
          </div>
          <p className="text-lg md:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Acompanhe dicas diárias sobre saúde pélvica, exercícios e bem-estar feminino
          </p>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6 mb-8 md:mb-12">
          {instagramPosts.map((post, index) => (
            <Card 
              key={post.id}
              className="group hover:shadow-xl transition-all duration-300 hover:-translate-y-2 border-none bg-white/70 backdrop-blur-sm overflow-hidden animate-fade-in"
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              <CardContent className="p-0">
                <div className="relative overflow-hidden">
                  <img 
                    src={post.image}
                    alt={post.caption}
                    className="w-full h-48 md:h-56 object-cover group-hover:scale-110 transition-transform duration-500"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  <div className="absolute top-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <a 
                      href={post.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="w-8 h-8 bg-white/90 rounded-full flex items-center justify-center hover:bg-white transition-colors"
                    >
                      <ExternalLink className="h-4 w-4 text-gray-700" />
                    </a>
                  </div>
                  <div className="absolute bottom-3 left-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <div className="flex items-center space-x-1 text-white">
                      <Heart className="h-4 w-4 fill-current" />
                      <span className="text-sm font-medium">{post.likes}</span>
                    </div>
                  </div>
                </div>
                <div className="p-4">
                  <p className="text-sm text-gray-600 line-clamp-2">{post.caption}</p>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="text-center">
          <Button 
            asChild
            size="lg"
            className="bg-gradient-to-r from-pink-500 to-purple-500 hover:from-pink-600 hover:to-purple-600 text-white px-8 py-3 text-lg"
          >
            <a 
              href="https://instagram.com/mariahblaj" 
              target="_blank" 
              rel="noopener noreferrer"
              className="flex items-center space-x-2"
            >
              <Instagram className="h-5 w-5" />
              <span>Seguir @mariahblaj</span>
              <ExternalLink className="h-4 w-4" />
            </a>
          </Button>
        </div>
      </div>
    </section>
  );
};

export default InstagramSection;
