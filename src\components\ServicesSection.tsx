
import { Card, CardContent } from "@/components/ui/card";
import { Heart, Activity, Flower, Zap, Target, Sparkles } from "lucide-react";

const services = [
  {
    icon: Heart,
    title: "Fisioterapia Integrativa Pélvica",
    description: "Tratamento especializado para disfunções do assoalho pélvico, incontinência urinária e disfunções sexuais.",
    color: "from-pink-500 to-rose-500"
  },
  {
    icon: Activity,
    title: "Pilates Terapêutico",
    description: "Fortalecimento do core e assoalho pélvico através de exercícios específicos e personalizados.",
    color: "from-purple-500 to-indigo-500"
  },
  {
    icon: Flower,
    title: "Yoga Hormonal",
    description: "Técnicas específicas para equilibrar hormônios naturalmente e melhorar a qualidade de vida.",
    color: "from-indigo-500 to-blue-500"
  },
  {
    icon: <PERSON>rk<PERSON>,
    title: "Aromaterapia",
    description: "Uso de óleos essenciais para promover relaxamento, alívio de tensões e bem-estar geral.",
    color: "from-green-500 to-teal-500"
  },
  {
    icon: Zap,
    title: "Acupuntura",
    description: "Medicina tradicional chinesa para tratamento de dores, ansiedade e desequilíbrios energéticos.",
    color: "from-yellow-500 to-orange-500"
  },
  {
    icon: Target,
    title: "Tratamento Personalizado",
    description: "Avaliação completa e plano de tratamento individualizado para suas necessidades específicas.",
    color: "from-pink-500 to-purple-500"
  }
];

const ServicesSection = () => {
  return (
    <section id="services" className="py-12 md:py-20 px-4">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-12 md:mb-16 animate-fade-in">
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-800 mb-4">
            Tratamentos <span className="text-pink-600">Especializados</span>
          </h2>
          <p className="text-lg md:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Oferecemos uma abordagem holística e integrativa, combinando técnicas tradicionais e modernas 
            para o seu bem-estar completo.
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8">
          {services.map((service, index) => (
            <Card 
              key={index}
              className="group hover:shadow-2xl transition-all duration-300 hover:-translate-y-2 border-none bg-white/70 backdrop-blur-sm animate-fade-in"
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              <CardContent className="p-6 md:p-8">
                <div className={`w-12 h-12 md:w-16 md:h-16 rounded-2xl bg-gradient-to-br ${service.color} flex items-center justify-center mb-4 md:mb-6 group-hover:scale-110 transition-transform duration-300`}>
                  <service.icon className="h-6 w-6 md:h-8 md:w-8 text-white" />
                </div>
                <h3 className="text-lg md:text-xl font-bold text-gray-800 mb-3 md:mb-4">
                  {service.title}
                </h3>
                <p className="text-sm md:text-base text-gray-600 leading-relaxed">
                  {service.description}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};

export default ServicesSection;
