import { Card, CardContent } from "@/components/ui/card";
import { Heart, Activity, Flower, Zap, Target, Sparkles } from "lucide-react";

const services = [
  {
    icon: Heart,
    title: "Fisioterapia Integrativa Pélvica",
    description:
      "Tratamento especializado para disfunções do assoalho pélvico, incontinência urinária e disfunções sexuais.",
    color: "from-pink-500 to-rose-500",
  },
  {
    icon: Activity,
    title: "Pilates Terapêutico",
    description:
      "Fortalecimento do core e assoalho pélvico através de exercícios específicos e personalizados.",
    color: "from-purple-500 to-indigo-500",
  },
  {
    icon: Flower,
    title: "Yoga Hormonal",
    description:
      "Técnicas específicas para equilibrar hormônios naturalmente e melhorar a qualidade de vida.",
    color: "from-indigo-500 to-blue-500",
  },
  {
    icon: Spark<PERSON>,
    title: "Aromaterapia",
    description:
      "Uso de óleos essenciais para promover relaxamento, alívio de tensões e bem-estar geral.",
    color: "from-green-500 to-teal-500",
  },
  {
    icon: Zap,
    title: "Acupuntura",
    description:
      "Medicina tradicional chinesa para tratamento de dores, ansiedade e desequilíbrios energéticos.",
    color: "from-yellow-500 to-orange-500",
  },
  {
    icon: Target,
    title: "Tratamento Personalizado",
    description:
      "Avaliação completa e plano de tratamento individualizado para suas necessidades específicas.",
    color: "from-pink-500 to-purple-500",
  },
];

const ServicesSection = () => {
  return (
    <section
      id="services"
      className="py-16 md:py-24 px-4 bg-gradient-to-br from-neutral-50 via-white to-brand-50/30"
    >
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-16 md:mb-20 animate-fade-in">
          <div className="inline-flex items-center bg-gradient-to-r from-brand-100 to-wellness-100 text-brand-700 px-4 py-2 rounded-full text-sm font-medium mb-6 shadow-soft">
            🌟 Tratamentos Especializados
          </div>
          <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold text-neutral-900 mb-6 tracking-tight">
            Cuidado <span className="text-primary">Integral</span> e
            Personalizado
          </h2>
          <p className="text-xl md:text-2xl text-neutral-600 max-w-4xl mx-auto leading-relaxed">
            Oferecemos uma abordagem holística e integrativa, combinando
            técnicas tradicionais e modernas para o seu bem-estar completo e
            qualidade de vida.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 lg:gap-10">
          {services.map((service, index) => (
            <Card
              key={index}
              className="group hover:shadow-large transition-all duration-500 hover:-translate-y-3 border-none bg-white/80 backdrop-blur-sm animate-fade-in-up overflow-hidden relative"
              style={{ animationDelay: `${index * 0.15}s` }}
            >
              {/* Gradient Border Effect */}
              <div className="absolute inset-0 bg-gradient-to-br from-primary/20 via-transparent to-wellness-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

              <CardContent className="p-8 md:p-10 relative z-10">
                {/* Icon Container */}
                <div className="relative mb-8">
                  <div
                    className={`w-16 h-16 md:w-20 md:h-20 rounded-3xl bg-gradient-to-br ${service.color} flex items-center justify-center mb-2 group-hover:scale-110 group-hover:rotate-3 transition-all duration-500 shadow-medium`}
                  >
                    <service.icon className="h-8 w-8 md:h-10 md:w-10 text-white" />
                  </div>
                  {/* Floating decoration */}
                  <div className="absolute -top-2 -right-2 w-6 h-6 bg-gradient-to-br from-brand-300 to-wellness-300 rounded-full opacity-60 group-hover:scale-125 transition-transform duration-500" />
                </div>

                {/* Content */}
                <div className="space-y-4">
                  <h3 className="text-xl md:text-2xl font-bold text-neutral-900 mb-4 group-hover:text-primary transition-colors duration-300">
                    {service.title}
                  </h3>
                  <p className="text-base md:text-lg text-neutral-600 leading-relaxed">
                    {service.description}
                  </p>
                </div>

                {/* Hover Arrow */}
                <div className="mt-6 opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-y-2 group-hover:translate-y-0">
                  <div className="inline-flex items-center text-primary font-medium">
                    Saiba mais
                    <svg
                      className="ml-2 w-4 h-4 transition-transform group-hover:translate-x-1"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 5l7 7-7 7"
                      />
                    </svg>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};

export default ServicesSection;
