
import { Card, CardContent } from "@/components/ui/card";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import { Star, Quote } from "lucide-react";

const testimonials = [
  {
    name: "Ana Carolina",
    age: 34,
    condition: "Incontinência Urinária",
    text: "Após 3 meses de tratamento com a Mariah, minha qualidade de vida mudou completamente. Hoje me sinto segura e confiante novamente. O atendimento é excepcional e os resultados surpreendentes.",
    rating: 5
  },
  {
    name: "<PERSON>",
    age: 28,
    condition: "Preparação para o Parto",
    text: "O trabalho de preparação pélvica que fizemos foi fundamental para um parto mais tranquilo. A Mariah me deu toda a orientação e confiança que eu precisava. Recomendo para todas as gestantes.",
    rating: 5
  },
  {
    name: "<PERSON> Fe<PERSON>",
    age: 45,
    condition: "Yoga Hormonal",
    text: "A yoga hormonal transformou minha menopausa. Os fogachos diminuíram drasticamente e meu humor melhorou muito. O ambiente do consultório é acolhedor e a Mariah é uma profissional incrível.",
    rating: 5
  },
  {
    name: "Roberta Lima",
    age: 32,
    condition: "Pilates Terapêutico",
    text: "Sofria com dores nas costas há anos. Com o pilates terapêutico, não só as dores sumiram como ganhei muito mais consciência corporal. Cada sessão é um momento de cuidado comigo mesma.",
    rating: 5
  },
  {
    name: "Camila Rodrigues",
    age: 29,
    condition: "Fisioterapia Pélvica",
    text: "Pensava que teria que conviver com o desconforto para sempre. A Mariah me mostrou que era possível melhorar e hoje me sinto uma nova mulher. Profissionalismo e empatia excepcionais.",
    rating: 5
  }
];

const TestimonialsSection = () => {
  return (
    <section className="py-12 md:py-20 px-4 bg-gradient-to-r from-pink-50 to-purple-50">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-12 md:mb-16 animate-fade-in">
          <div className="flex items-center justify-center space-x-2 mb-4">
            <Quote className="h-6 w-6 md:h-8 md:w-8 text-pink-600" />
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-800">
              O Que Dizem Nossas <span className="text-pink-600">Pacientes</span>
            </h2>
          </div>
          <p className="text-lg md:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Histórias reais de transformação e bem-estar. Veja como nossos tratamentos 
            mudaram a vida dessas mulheres incríveis.
          </p>
        </div>

        <div className="animate-fade-in">
          <Carousel className="w-full max-w-5xl mx-auto">
            <CarouselContent className="-ml-2 md:-ml-4">
              {testimonials.map((testimonial, index) => (
                <CarouselItem key={index} className="pl-2 md:pl-4 basis-full md:basis-1/2">
                  <div className="p-2">
                    <Card className="h-full border-none shadow-lg hover:shadow-xl transition-all duration-300 bg-white/80 backdrop-blur-sm">
                      <CardContent className="p-6 md:p-8">
                        <div className="flex items-center justify-between mb-4">
                          <div className="flex space-x-1">
                            {[...Array(testimonial.rating)].map((_, i) => (
                              <Star key={i} className="h-4 w-4 md:h-5 md:w-5 fill-yellow-400 text-yellow-400" />
                            ))}
                          </div>
                          <Quote className="h-5 w-5 md:h-6 md:w-6 text-pink-600 opacity-60" />
                        </div>
                        
                        <p className="text-gray-700 leading-relaxed mb-6 text-base md:text-lg italic">
                          "{testimonial.text}"
                        </p>
                        
                        <div className="border-t border-gray-100 pt-4">
                          <h4 className="font-bold text-gray-800 text-base md:text-lg mb-1">{testimonial.name}</h4>
                          <p className="text-xs md:text-sm text-gray-500 mb-1">{testimonial.age} anos</p>
                          <p className="text-xs md:text-sm text-pink-600 font-medium">{testimonial.condition}</p>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                </CarouselItem>
              ))}
            </CarouselContent>
            <CarouselPrevious className="hidden md:flex left-2 md:left-4" />
            <CarouselNext className="hidden md:flex right-2 md:right-4" />
          </Carousel>
        </div>
      </div>
    </section>
  );
};

export default TestimonialsSection;
