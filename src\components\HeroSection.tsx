import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";

const HeroSection = () => {
  return (
    <section className="relative min-h-screen flex items-center justify-center px-4 py-12 md:py-20 overflow-hidden">
      {/* Enhanced Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-brand-50 via-white to-wellness-50" />
      <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top_right,_var(--tw-gradient-stops))] from-brand-100/30 via-transparent to-wellness-100/20" />

      {/* Floating Elements */}
      <div className="absolute top-20 left-10 w-20 h-20 bg-gradient-to-br from-brand-200 to-brand-300 rounded-full opacity-20 animate-float" />
      <div className="absolute bottom-32 right-16 w-16 h-16 bg-gradient-to-br from-wellness-200 to-wellness-300 rounded-full opacity-25 animate-gentle-bounce" />
      <div
        className="absolute top-1/3 right-1/4 w-12 h-12 bg-gradient-to-br from-brand-300 to-wellness-300 rounded-full opacity-15 animate-float"
        style={{ animationDelay: "1s" }}
      />

      <div className="relative z-10 max-w-7xl mx-auto grid lg:grid-cols-2 gap-12 lg:gap-16 items-center">
        <div className="space-y-8 animate-fade-in text-center lg:text-left">
          {/* Badge */}
          <div className="inline-flex items-center bg-gradient-to-r from-brand-100 to-wellness-100 text-brand-700 px-4 py-2 rounded-full text-sm font-medium shadow-soft">
            ✨ Especialista em Saúde Pélvica Feminina
          </div>

          {/* Main Heading */}
          <div className="space-y-6">
            <h1 className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-bold text-neutral-900 leading-tight tracking-tight">
              <span className="block text-primary font-logo">Mariah Blaj</span>
              <span className="block text-2xl sm:text-3xl md:text-4xl lg:text-5xl text-neutral-700 font-sans font-semibold mt-2">
                Fisioterapeuta Pélvica
              </span>
            </h1>
            <p className="text-lg md:text-xl lg:text-2xl text-neutral-600 leading-relaxed max-w-2xl mx-auto lg:mx-0">
              Transformo vidas através de cuidado integral e personalizado para
              o seu bem-estar íntimo e qualidade de vida em São Paulo.
            </p>
          </div>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 lg:gap-6 justify-center lg:justify-start">
            <Button
              size="lg"
              className="bg-gradient-to-r from-primary to-brand-600 hover:from-primary/90 hover:to-brand-700 text-white px-8 py-4 rounded-full text-lg font-semibold group transition-all duration-300 hover:scale-105 hover:shadow-large w-full sm:w-auto"
              onClick={() =>
                document
                  .getElementById("contact")
                  ?.scrollIntoView({ behavior: "smooth" })
              }
            >
              Agendar Consulta
              <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
            </Button>
            <Button
              variant="outline"
              size="lg"
              className="border-2 border-primary text-primary hover:bg-primary hover:text-white px-8 py-4 rounded-full text-lg font-semibold transition-all duration-300 hover:scale-105 w-full sm:w-auto"
              onClick={() =>
                document
                  .getElementById("services")
                  ?.scrollIntoView({ behavior: "smooth" })
              }
            >
              Conhecer Tratamentos
            </Button>
          </div>

          {/* Trust Indicators */}
          <div className="flex flex-col sm:flex-row items-center justify-center lg:justify-start space-y-4 sm:space-y-0 sm:space-x-8 pt-4">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-primary/10 rounded-full">
                <Phone className="h-5 w-5 text-primary" />
              </div>
              <span className="text-base text-neutral-700 font-medium">
                Consultas Presenciais
              </span>
            </div>
            <div className="flex items-center space-x-3">
              <div className="flex -space-x-1">
                {[1, 2, 3, 4, 5].map((star) => (
                  <Star
                    key={star}
                    className="h-5 w-5 text-yellow-400 fill-current"
                  />
                ))}
              </div>
              <span className="text-base text-neutral-700 font-medium">
                Avaliação 5 estrelas
              </span>
            </div>
          </div>
        </div>

        {/* Hero Image */}
        <div className="relative animate-slide-in-right order-first lg:order-last">
          <div className="relative z-10 bg-white rounded-4xl p-6 md:p-8 shadow-large">
            <img
              src="https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
              alt="Profissional de fisioterapia"
              className="w-full h-80 md:h-96 lg:h-[500px] object-cover rounded-3xl"
            />

            {/* Floating Stats Cards */}
            <div className="absolute -bottom-4 -right-4 bg-gradient-to-r from-primary to-brand-600 text-white p-4 md:p-6 rounded-2xl shadow-large">
              <p className="font-bold text-lg md:text-xl">São Paulo</p>
              <p className="text-brand-100 text-sm md:text-base">
                Atendimento Especializado
              </p>
            </div>

            <div className="absolute -top-4 -left-4 bg-white p-4 rounded-2xl shadow-medium">
              <div className="text-center">
                <div className="text-2xl font-bold text-primary">500+</div>
                <div className="text-xs text-neutral-600">Pacientes</div>
              </div>
            </div>
          </div>

          {/* Background Decorations */}
          <div className="absolute -top-8 -right-8 w-24 h-24 md:w-32 md:h-32 bg-gradient-to-br from-brand-200 to-brand-300 rounded-full opacity-20 animate-gentle-bounce" />
          <div
            className="absolute -bottom-12 -left-12 w-20 h-20 md:w-28 md:h-28 bg-gradient-to-br from-wellness-200 to-wellness-300 rounded-full opacity-25 animate-float"
            style={{ animationDelay: "2s" }}
          />
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
