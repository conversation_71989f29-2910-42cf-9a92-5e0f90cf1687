import {
  <PERSON><PERSON><PERSON>,
  Phone,
  Heart,
  Star,
  Clock,
  MapPin,
  Award,
  Users,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";

const HeroSection = () => {
  return (
    <div className="space-y-0">
      {/* Hero Variation 1 - Current/Original with Logo Colors */}
      <section className="relative min-h-screen flex items-center justify-center px-4 py-12 md:py-20">
        <div className="absolute inset-0 bg-gradient-to-br from-logo-rose/20 via-white to-logo-emerald/10" />

        <div className="relative z-10 max-w-6xl mx-auto grid lg:grid-cols-2 gap-8 md:gap-12 items-center">
          <div className="space-y-6 md:space-y-8 animate-fade-in text-center lg:text-left">
            <div className="space-y-4">
              <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-logo font-bold text-gray-800 leading-tight">
                <span className="text-logo-emerald"><PERSON><PERSON>j</span>
                <br />
                <span className="text-xl sm:text-2xl md:text-3xl lg:text-4xl text-gray-700 font-sans">
                  Fisioterapeuta Pélvica
                </span>
              </h1>
              <p className="text-base sm:text-lg md:text-xl text-gray-600 leading-relaxed max-w-lg mx-auto lg:mx-0 font-sans">
                Cuidado integral e personalizado para o seu bem-estar íntimo e
                qualidade de vida através de terapias holísticas em São Paulo.
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-3 md:gap-4 justify-center lg:justify-start">
              <Button
                size="lg"
                className="bg-gradient-to-r from-logo-emerald to-logo-emerald-dark hover:from-logo-emerald-dark hover:to-logo-emerald text-white px-6 md:px-8 py-3 md:py-4 rounded-full text-base md:text-lg font-semibold group transition-all duration-300 hover:scale-105 w-full sm:w-auto"
                onClick={() =>
                  document
                    .getElementById("contact")
                    ?.scrollIntoView({ behavior: "smooth" })
                }
              >
                Agendar Consulta
                <ArrowRight className="ml-2 h-4 w-4 md:h-5 md:w-5 group-hover:translate-x-1 transition-transform" />
              </Button>
              <Button
                variant="outline"
                size="lg"
                className="border-logo-emerald text-logo-emerald hover:bg-logo-emerald/10 px-6 md:px-8 py-3 md:py-4 rounded-full text-base md:text-lg font-semibold w-full sm:w-auto"
                onClick={() =>
                  document
                    .getElementById("services")
                    ?.scrollIntoView({ behavior: "smooth" })
                }
              >
                Conhecer Tratamentos
              </Button>
            </div>

            <div className="flex flex-col sm:flex-row items-center justify-center lg:justify-start space-y-4 sm:space-y-0 sm:space-x-6 pt-4">
              <div className="flex items-center space-x-2">
                <Phone className="h-4 w-4 md:h-5 md:w-5 text-logo-emerald" />
                <span className="text-sm md:text-base text-gray-700 font-medium">
                  Consultas Presenciais
                </span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="flex -space-x-1">
                  {[1, 2, 3, 4, 5].map((star) => (
                    <div
                      key={star}
                      className="h-4 w-4 md:h-5 md:w-5 bg-yellow-400 rounded-full flex items-center justify-center"
                    >
                      <span className="text-xs text-white">★</span>
                    </div>
                  ))}
                </div>
                <span className="text-sm md:text-base text-gray-700 font-medium">
                  Avaliação 5 estrelas
                </span>
              </div>
            </div>
          </div>

          <div className="relative animate-fade-in order-first lg:order-last">
            <div className="relative z-10 bg-white rounded-3xl p-4 md:p-8 shadow-2xl">
              <img
                src="https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                alt="Profissional de fisioterapia"
                className="w-full h-64 md:h-96 object-cover rounded-2xl"
              />
              <div className="absolute -bottom-2 md:-bottom-4 -right-2 md:-right-4 bg-logo-emerald text-white p-3 md:p-4 rounded-2xl">
                <p className="font-semibold text-sm md:text-lg">São Paulo</p>
                <p className="text-emerald-100 text-xs md:text-base">
                  Atendimento Especializado
                </p>
              </div>
            </div>
            <div className="absolute -top-4 -left-4 w-16 h-16 md:w-32 md:h-32 bg-gradient-to-br from-logo-rose to-logo-pink rounded-full opacity-30 animate-pulse" />
            <div className="absolute -bottom-8 -left-8 w-12 h-12 md:w-24 md:h-24 bg-gradient-to-br from-logo-emerald to-logo-emerald-dark rounded-full opacity-30 animate-pulse delay-1000" />
          </div>
        </div>
      </section>

      {/* Hero Variation 2 - Centered with Stats */}
      <section className="relative min-h-screen flex items-center justify-center px-4 py-12 bg-gradient-to-br from-rose-50 via-pink-50 to-purple-100">
        <div className="max-w-5xl mx-auto text-center space-y-12">
          <div className="space-y-8 animate-fade-in">
            <div className="inline-flex items-center bg-pink-100 text-pink-700 px-6 py-2 rounded-full text-sm font-medium">
              ✨ Especialista em Saúde Pélvica Feminina
            </div>

            <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold text-gray-900 leading-tight">
              <span className="block text-pink-600">Mariah Blaj</span>
              <span className="block text-2xl md:text-3xl lg:text-4xl text-gray-600 mt-4">
                Transformando vidas através do cuidado integral
              </span>
            </h1>

            <p className="text-lg md:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Tratamentos personalizados com técnicas modernas e abordagem
              holística para sua saúde íntima e bem-estar em São Paulo.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Button
                size="lg"
                className="bg-gradient-to-r from-pink-600 to-rose-600 hover:from-pink-700 hover:to-rose-700 text-white px-8 py-4 rounded-full text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300"
              >
                Agendar Avaliação Gratuita
                <Heart className="ml-2 h-5 w-5" />
              </Button>
              <Button
                variant="outline"
                size="lg"
                className="border-2 border-pink-600 text-pink-600 hover:bg-pink-600 hover:text-white px-8 py-4 rounded-full text-lg font-semibold transition-all duration-300"
              >
                Ver Tratamentos
              </Button>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg">
              <div className="text-3xl font-bold text-pink-600 mb-2">500+</div>
              <div className="text-gray-700">Pacientes Atendidas</div>
            </div>
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg">
              <div className="text-3xl font-bold text-pink-600 mb-2">8+</div>
              <div className="text-gray-700">Anos de Experiência</div>
            </div>
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg">
              <div className="text-3xl font-bold text-pink-600 mb-2">98%</div>
              <div className="text-gray-700">Satisfação</div>
            </div>
          </div>
        </div>
      </section>

      {/* Hero Variation 4 - Minimal with Floating Cards */}
      <section className="relative min-h-screen flex items-center justify-center px-4 py-12 bg-gray-50">
        <div className="max-w-6xl mx-auto">
          <div className="text-center space-y-12">
            <div className="space-y-8 animate-fade-in">
              <h1 className="text-5xl md:text-7xl font-light text-gray-900 leading-tight">
                <span className="font-bold text-pink-600">Mariah Blaj</span>
                <br />
                <span className="text-3xl md:text-4xl text-gray-500">
                  Fisioterapia Pélvica
                </span>
              </h1>

              <p className="text-xl text-gray-600 max-w-2xl mx-auto">
                Especialista em saúde pélvica feminina com abordagem integrativa
                e personalizada
              </p>

              <Button
                size="lg"
                className="bg-pink-600 hover:bg-pink-700 text-white px-12 py-4 rounded-full text-lg font-medium shadow-lg hover:shadow-xl transition-all duration-300"
              >
                Começar Tratamento
              </Button>
            </div>

            <div className="grid md:grid-cols-3 gap-8 max-w-4xl mx-auto">
              <div className="bg-white rounded-3xl p-8 shadow-xl hover:shadow-2xl transition-all duration-300 hover:-translate-y-2">
                <Award className="h-12 w-12 text-pink-600 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  Especialização
                </h3>
                <p className="text-gray-600">
                  Certificada em Fisioterapia Pélvica e técnicas avançadas
                </p>
              </div>

              <div className="bg-white rounded-3xl p-8 shadow-xl hover:shadow-2xl transition-all duration-300 hover:-translate-y-2">
                <Users className="h-12 w-12 text-pink-600 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  Experiência
                </h3>
                <p className="text-gray-600">
                  Mais de 500 pacientes atendidas com excelência
                </p>
              </div>

              <div className="bg-white rounded-3xl p-8 shadow-xl hover:shadow-2xl transition-all duration-300 hover:-translate-y-2">
                <Star className="h-12 w-12 text-pink-600 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  Qualidade
                </h3>
                <p className="text-gray-600">
                  Avaliação 5 estrelas e alta taxa de satisfação
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Hero Variation 5 - Video Background Style */}
      <section className="relative min-h-screen flex items-center justify-center px-4 py-12">
        <div className="absolute inset-0 bg-gradient-to-br from-pink-900/90 via-purple-900/80 to-indigo-900/90" />
        <div className="absolute inset-0 bg-[url('https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80')] bg-cover bg-center opacity-20" />

        <div className="relative z-10 max-w-4xl mx-auto text-center space-y-8 text-white">
          <div className="space-y-6 animate-fade-in">
            <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold leading-tight">
              <span className="block">Bem-estar íntimo</span>
              <span className="block text-pink-300">que transforma vidas</span>
            </h1>

            <p className="text-xl md:text-2xl text-gray-200 max-w-3xl mx-auto leading-relaxed">
              Sou Mariah Blaj, especialista em fisioterapia pélvica, dedicada a
              oferecer cuidado integral e personalizado para sua saúde feminina.
            </p>
          </div>

          <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
            <Button
              size="lg"
              className="bg-white text-pink-600 hover:bg-gray-100 px-8 py-4 rounded-full text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300"
            >
              Agendar Consulta
              <ArrowRight className="ml-2 h-5 w-5" />
            </Button>
            <Button
              variant="outline"
              size="lg"
              className="border-2 border-white text-white hover:bg-white hover:text-pink-600 px-8 py-4 rounded-full text-lg font-semibold transition-all duration-300"
            >
              Saber Mais
            </Button>
          </div>

          <div className="flex justify-center items-center space-x-8 pt-8">
            <div className="text-center">
              <div className="text-2xl font-bold text-pink-300">8+</div>
              <div className="text-sm text-gray-300">Anos</div>
            </div>
            <div className="w-px h-8 bg-gray-400"></div>
            <div className="text-center">
              <div className="text-2xl font-bold text-pink-300">500+</div>
              <div className="text-sm text-gray-300">Pacientes</div>
            </div>
            <div className="w-px h-8 bg-gray-400"></div>
            <div className="text-center">
              <div className="text-2xl font-bold text-pink-300">98%</div>
              <div className="text-sm text-gray-300">Satisfação</div>
            </div>
          </div>
        </div>
      </section>

      {/* Hero Variation 6 - Card-based Layout */}
      <section className="relative min-h-screen flex items-center justify-center px-4 py-12 bg-gradient-to-br from-purple-50 via-pink-50 to-rose-50">
        <div className="max-w-7xl mx-auto">
          <div className="grid lg:grid-cols-12 gap-8 items-center">
            <div className="lg:col-span-5 space-y-8">
              <div className="space-y-6">
                <div className="inline-flex items-center bg-pink-600 text-white px-4 py-2 rounded-full text-sm font-medium">
                  👩‍⚕️ Especialista Certificada
                </div>

                <h1 className="text-4xl lg:text-5xl font-bold text-gray-900 leading-tight">
                  <span className="text-pink-600">Mariah Blaj</span>
                  <br />
                  Fisioterapeuta Pélvica
                </h1>

                <p className="text-lg text-gray-600 leading-relaxed">
                  Transformo vidas através de tratamentos personalizados em
                  fisioterapia pélvica, pilates terapêutico e terapias
                  holísticas.
                </p>
              </div>

              <div className="flex flex-col sm:flex-row gap-4">
                <Button
                  size="lg"
                  className="bg-pink-600 hover:bg-pink-700 text-white px-6 py-3 rounded-xl font-semibold group"
                >
                  Agendar Avaliação
                  <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                </Button>
                <Button
                  variant="outline"
                  size="lg"
                  className="border-pink-600 text-pink-600 hover:bg-pink-50 px-6 py-3 rounded-xl font-semibold"
                >
                  WhatsApp
                </Button>
              </div>
            </div>

            <div className="lg:col-span-7">
              <div className="relative">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-4">
                    <div className="bg-white rounded-2xl p-6 shadow-lg">
                      <img
                        src="https://images.unsplash.com/photo-1559757175-0eb30cd8c063?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80"
                        alt="Tratamento"
                        className="w-full h-32 object-cover rounded-xl mb-4"
                      />
                      <h3 className="font-semibold text-gray-900 mb-2">
                        Fisioterapia Pélvica
                      </h3>
                      <p className="text-sm text-gray-600">
                        Tratamento especializado para disfunções do assoalho
                        pélvico
                      </p>
                    </div>

                    <div className="bg-gradient-to-br from-pink-600 to-rose-600 rounded-2xl p-6 text-white">
                      <div className="text-2xl font-bold mb-2">500+</div>
                      <div className="text-pink-100">
                        Pacientes atendidas com sucesso
                      </div>
                    </div>
                  </div>

                  <div className="space-y-4 pt-8">
                    <div className="bg-gradient-to-br from-purple-600 to-indigo-600 rounded-2xl p-6 text-white">
                      <div className="text-2xl font-bold mb-2">8+</div>
                      <div className="text-purple-100">
                        Anos de experiência especializada
                      </div>
                    </div>

                    <div className="bg-white rounded-2xl p-6 shadow-lg">
                      <img
                        src="https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80"
                        alt="Ambiente"
                        className="w-full h-32 object-cover rounded-xl mb-4"
                      />
                      <h3 className="font-semibold text-gray-900 mb-2">
                        Ambiente Acolhedor
                      </h3>
                      <p className="text-sm text-gray-600">
                        Espaço preparado para seu conforto e privacidade
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default HeroSection;
