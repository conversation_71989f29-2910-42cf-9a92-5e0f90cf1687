
import { Card, CardContent } from "@/components/ui/card";
import { Leaf, Heart, Shield, Sparkles, Sun, Wind } from "lucide-react";

const environmentFeatures = [
  {
    icon: Leaf,
    title: "Ambiente Natural",
    description: "Plantas e elementos naturais que promovem tranquilidade e bem-estar"
  },
  {
    icon: Sun,
    title: "Iluminação Natural",
    description: "Luz natural abundante para criar um ambiente acolhedor e energizante"
  },
  {
    icon: Wind,
    title: "Ventilação Pura",
    description: "Sistema de ventilação que garante ar puro e renovado constantemente"
  },
  {
    icon: Shield,
    title: "Privacidade Total",
    description: "Ambiente reservado e discreto para garantir seu conforto e privacidade"
  },
  {
    icon: Heart,
    title: "Energia Positiva",
    description: "Espaço energeticamente harmonizado com cristais e aromas terapêuticos"
  },
  {
    icon: Sparkles,
    title: "Higienização Completa",
    description: "Protocolos rigorosos de limpeza e sanitização entre cada atendimento"
  }
];

const EnvironmentSection = () => {
  return (
    <section className="py-12 md:py-20 px-4">
      <div className="max-w-6xl mx-auto">
        <div className="grid lg:grid-cols-2 gap-8 md:gap-16 items-center">
          <div className="space-y-6 md:space-y-8 animate-fade-in order-2 lg:order-1">
            <div>
              <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-800 mb-4 md:mb-6 text-center lg:text-left">
                Nosso <span className="text-pink-600">Ambiente</span>
              </h2>
              <p className="text-lg md:text-xl text-gray-600 leading-relaxed mb-6 md:mb-8 text-center lg:text-left">
                Criamos um espaço especialmente pensado para promover seu bem-estar, 
                combinando conforto, privacidade e energia positiva em cada detalhe.
              </p>
            </div>
            
            <div className="grid gap-4 md:gap-6">
              {environmentFeatures.map((feature, index) => (
                <div 
                  key={index}
                  className="flex items-start space-x-3 md:space-x-4 p-3 md:p-4 rounded-2xl bg-white/50 backdrop-blur-sm hover:bg-white/80 transition-all duration-300 hover:shadow-lg"
                  style={{ animationDelay: `${index * 0.1}s` }}
                >
                  <div className="w-10 h-10 md:w-12 md:h-12 bg-gradient-to-br from-pink-500 to-purple-500 rounded-xl flex items-center justify-center flex-shrink-0">
                    <feature.icon className="h-5 w-5 md:h-6 md:w-6 text-white" />
                  </div>
                  <div>
                    <h3 className="text-base md:text-lg font-bold text-gray-800 mb-1 md:mb-2">{feature.title}</h3>
                    <p className="text-sm md:text-base text-gray-600 leading-relaxed">{feature.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
          
          <div className="relative animate-fade-in order-1 lg:order-2">
            <div className="grid grid-cols-2 gap-2 md:gap-4">
              <div className="space-y-2 md:space-y-4">
                <img 
                  src="https://images.unsplash.com/photo-1721322800607-8c38375eef04?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80"
                  alt="Ambiente do consultório"
                  className="w-full h-32 md:h-48 object-cover rounded-2xl shadow-lg hover:shadow-xl transition-shadow duration-300"
                />
                <img 
                  src="https://images.unsplash.com/photo-1465146344425-f00d5f5c8f07?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80"
                  alt="Decoração natural"
                  className="w-full h-24 md:h-32 object-cover rounded-2xl shadow-lg hover:shadow-xl transition-shadow duration-300"
                />
              </div>
              <div className="space-y-2 md:space-y-4 mt-4 md:mt-8">
                <img 
                  src="https://images.unsplash.com/photo-1500673922987-e212871fec22?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80"
                  alt="Ambiente relaxante"
                  className="w-full h-24 md:h-32 object-cover rounded-2xl shadow-lg hover:shadow-xl transition-shadow duration-300"
                />
                <img 
                  src="https://images.unsplash.com/photo-1649972904349-6e44c42644a7?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80"
                  alt="Espaço de atendimento"
                  className="w-full h-32 md:h-48 object-cover rounded-2xl shadow-lg hover:shadow-xl transition-shadow duration-300"
                />
              </div>
            </div>
            
            <div className="absolute -top-2 md:-top-4 -right-2 md:-right-4 w-12 h-12 md:w-24 md:h-24 bg-gradient-to-br from-pink-400 to-purple-400 rounded-full opacity-20 animate-pulse" />
            <div className="absolute -bottom-2 md:-bottom-4 -left-2 md:-left-4 w-16 h-16 md:w-32 md:h-32 bg-gradient-to-br from-purple-400 to-indigo-400 rounded-full opacity-20 animate-pulse delay-1000" />
          </div>
        </div>
      </div>
    </section>
  );
};

export default EnvironmentSection;
