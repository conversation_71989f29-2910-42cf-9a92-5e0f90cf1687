
import { useState } from "react";
import { Link, useLocation } from "react-router-dom";
import { Menu, X, Instagram } from "lucide-react";
import { Button } from "@/components/ui/button";

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const location = useLocation();

  const isActive = (path: string) => location.pathname === path;

  return (
    <header className="fixed top-0 left-0 right-0 z-50 bg-white/95 backdrop-blur-md border-b border-pink-100 shadow-sm">
      <div className="max-w-6xl mx-auto px-4">
        <div className="flex items-center justify-between h-16 md:h-20">
          <Link to="/" className="flex items-center space-x-3 group">
            <img 
              src="/lovable-uploads/07290cf7-c21d-4b35-9492-f1ced0d84aa2.png" 
              alt="<PERSON><PERSON>"
              className="h-12 md:h-14 w-auto group-hover:scale-105 transition-transform duration-300"
            />
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            <Link 
              to="/" 
              className={`text-sm font-medium transition-colors hover:text-emerald-600 ${
                isActive('/') ? 'text-emerald-600' : 'text-gray-600'
              }`}
            >
              Início
            </Link>
            <Link 
              to="/links" 
              className={`text-sm font-medium transition-colors hover:text-emerald-600 ${
                isActive('/links') ? 'text-emerald-600' : 'text-gray-600'
              }`}
            >
              Links
            </Link>
            <a 
              href="https://instagram.com/mariahblaj" 
              target="_blank" 
              rel="noopener noreferrer"
              className="text-gray-600 hover:text-emerald-600 transition-colors"
            >
              <Instagram className="h-5 w-5" />
            </a>
            <Button 
              asChild
              className="bg-gradient-to-r from-rose-500 to-pink-500 hover:from-rose-600 hover:to-pink-600 text-white"
            >
              <a href="https://wa.me/5511999999999" target="_blank" rel="noopener noreferrer">
                Agendar Consulta
              </a>
            </Button>
          </nav>

          {/* Mobile Menu Button */}
          <button
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            className="md:hidden p-2 text-gray-600 hover:text-emerald-600 transition-colors"
          >
            {isMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
          </button>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden py-4 border-t border-pink-100 bg-white/95 backdrop-blur-md">
            <nav className="flex flex-col space-y-4">
              <Link 
                to="/" 
                onClick={() => setIsMenuOpen(false)}
                className={`text-sm font-medium transition-colors hover:text-emerald-600 ${
                  isActive('/') ? 'text-emerald-600' : 'text-gray-600'
                }`}
              >
                Início
              </Link>
              <Link 
                to="/links" 
                onClick={() => setIsMenuOpen(false)}
                className={`text-sm font-medium transition-colors hover:text-emerald-600 ${
                  isActive('/links') ? 'text-emerald-600' : 'text-gray-600'
                }`}
              >
                Links
              </Link>
              <a 
                href="https://instagram.com/mariahblaj" 
                target="_blank" 
                rel="noopener noreferrer"
                className="text-gray-600 hover:text-emerald-600 transition-colors flex items-center space-x-2"
              >
                <Instagram className="h-4 w-4" />
                <span className="text-sm">Instagram</span>
              </a>
              <Button 
                asChild
                className="bg-gradient-to-r from-rose-500 to-pink-500 hover:from-rose-600 hover:to-pink-600 text-white w-full"
              >
                <a href="https://wa.me/5511999999999" target="_blank" rel="noopener noreferrer">
                  Agendar Consulta
                </a>
              </Button>
            </nav>
          </div>
        )}
      </div>
    </header>
  );
};

export default Header;
