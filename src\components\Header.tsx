import { useState } from "react";
import { Link, useLocation } from "react-router-dom";
import { Menu, X, Instagram } from "lucide-react";
import { Button } from "@/components/ui/button";

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const location = useLocation();

  const isActive = (path: string) => location.pathname === path;

  return (
    <header className="fixed top-0 left-0 right-0 z-50 bg-white/95 backdrop-blur-xl border-b border-neutral-200/50 shadow-soft">
      <div className="max-w-7xl mx-auto px-4 lg:px-6">
        <div className="flex items-center justify-between h-16 md:h-20">
          <Link to="/" className="flex items-center space-x-3 group">
            <img
              src="/lovable-uploads/07290cf7-c21d-4b35-9492-f1ced0d84aa2.png"
              alt="<PERSON>h <PERSON><PERSON>"
              className="h-12 md:h-16 w-auto group-hover:scale-105 transition-all duration-300 drop-shadow-sm"
            />
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-6 lg:space-x-8">
            <Link
              to="/"
              className={`text-sm font-medium transition-all duration-300 hover:text-primary relative group ${
                isActive("/") ? "text-primary" : "text-neutral-600"
              }`}
            >
              Início
              <span
                className={`absolute -bottom-1 left-0 w-0 h-0.5 bg-primary transition-all duration-300 group-hover:w-full ${
                  isActive("/") ? "w-full" : ""
                }`}
              />
            </Link>
            <Link
              to="/links"
              className={`text-sm font-medium transition-all duration-300 hover:text-primary relative group ${
                isActive("/links") ? "text-primary" : "text-neutral-600"
              }`}
            >
              Links
              <span
                className={`absolute -bottom-1 left-0 w-0 h-0.5 bg-primary transition-all duration-300 group-hover:w-full ${
                  isActive("/links") ? "w-full" : ""
                }`}
              />
            </Link>
            <a
              href="https://instagram.com/mariahblaj"
              target="_blank"
              rel="noopener noreferrer"
              className="text-neutral-600 hover:text-primary transition-all duration-300 p-2 rounded-full hover:bg-primary/5"
              aria-label="Instagram"
            >
              <Instagram className="h-5 w-5" />
            </a>
            <Button
              asChild
              className="bg-gradient-to-r from-primary to-brand-600 hover:from-primary/90 hover:to-brand-700 text-white shadow-medium hover:shadow-large transition-all duration-300 rounded-full px-6 py-2.5 font-semibold"
            >
              <a
                href="https://wa.me/5511999999999"
                target="_blank"
                rel="noopener noreferrer"
              >
                Agendar Consulta
              </a>
            </Button>
          </nav>

          {/* Mobile Menu Button */}
          <button
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            className="md:hidden p-2 text-neutral-600 hover:text-primary transition-all duration-300 rounded-lg hover:bg-primary/5"
            aria-label="Toggle menu"
          >
            {isMenuOpen ? (
              <X className="h-6 w-6" />
            ) : (
              <Menu className="h-6 w-6" />
            )}
          </button>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden py-6 border-t border-neutral-200/50 bg-white/95 backdrop-blur-xl animate-fade-in-up">
            <nav className="flex flex-col space-y-6">
              <Link
                to="/"
                onClick={() => setIsMenuOpen(false)}
                className={`text-base font-medium transition-all duration-300 hover:text-primary px-2 py-1 rounded-lg hover:bg-primary/5 ${
                  isActive("/")
                    ? "text-primary bg-primary/5"
                    : "text-neutral-600"
                }`}
              >
                Início
              </Link>
              <Link
                to="/links"
                onClick={() => setIsMenuOpen(false)}
                className={`text-base font-medium transition-all duration-300 hover:text-primary px-2 py-1 rounded-lg hover:bg-primary/5 ${
                  isActive("/links")
                    ? "text-primary bg-primary/5"
                    : "text-neutral-600"
                }`}
              >
                Links
              </Link>
              <a
                href="https://instagram.com/mariahblaj"
                target="_blank"
                rel="noopener noreferrer"
                className="text-neutral-600 hover:text-primary transition-all duration-300 flex items-center space-x-3 px-2 py-1 rounded-lg hover:bg-primary/5"
              >
                <Instagram className="h-5 w-5" />
                <span className="text-base font-medium">Instagram</span>
              </a>
              <Button
                asChild
                className="bg-gradient-to-r from-primary to-brand-600 hover:from-primary/90 hover:to-brand-700 text-white w-full shadow-medium hover:shadow-large transition-all duration-300 rounded-full py-3 font-semibold"
              >
                <a
                  href="https://wa.me/5511999999999"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  Agendar Consulta
                </a>
              </Button>
            </nav>
          </div>
        )}
      </div>
    </header>
  );
};

export default Header;
