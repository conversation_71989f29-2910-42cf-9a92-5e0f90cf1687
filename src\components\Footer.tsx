
import { Heart, Instagram, MessageCircle, Mail } from "lucide-react";

const Footer = () => {
  return (
    <footer className="bg-gradient-to-br from-white via-gray-50 to-pink-50 text-gray-800 py-12 md:py-16 px-4 border-t border-gray-100">
      <div className="max-w-6xl mx-auto">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 md:gap-12">
          <div className="space-y-6 text-center md:text-left">
            <h3 className="text-2xl md:text-3xl font-bold text-pink-600">Mariah Blaj</h3>
            <p className="text-gray-600 leading-relaxed text-base md:text-lg">
              Fisioterapeuta especializada em saúde pélvica, oferecendo cuidado integral 
              e personalizado em São Paulo.
            </p>
            <div className="flex space-x-6 justify-center md:justify-start">
              <a 
                href="https://instagram.com/mariahblaj" 
                className="w-12 h-12 bg-gradient-to-br from-pink-500 to-purple-500 rounded-full flex items-center justify-center text-white hover:scale-110 transition-transform duration-300 shadow-md hover:shadow-lg"
              >
                <Instagram className="h-5 w-5" />
              </a>
              <a 
                href="https://wa.me/5511999999999" 
                className="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center text-white hover:scale-110 transition-transform duration-300 shadow-md hover:shadow-lg"
              >
                <MessageCircle className="h-5 w-5" />
              </a>
              <a 
                href="mailto:<EMAIL>" 
                className="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center text-white hover:scale-110 transition-transform duration-300 shadow-md hover:shadow-lg"
              >
                <Mail className="h-5 w-5" />
              </a>
            </div>
          </div>
          
          <div className="space-y-6 text-center md:text-left">
            <h4 className="text-lg md:text-xl font-bold text-pink-600">Tratamentos</h4>
            <ul className="space-y-3 text-gray-600">
              <li><a href="#services" className="hover:text-pink-600 transition-colors duration-300 flex items-center justify-center md:justify-start">Fisioterapia Pélvica</a></li>
              <li><a href="#services" className="hover:text-pink-600 transition-colors duration-300 flex items-center justify-center md:justify-start">Pilates Terapêutico</a></li>
              <li><a href="#services" className="hover:text-pink-600 transition-colors duration-300 flex items-center justify-center md:justify-start">Yoga Hormonal</a></li>
              <li><a href="#services" className="hover:text-pink-600 transition-colors duration-300 flex items-center justify-center md:justify-start">Aromaterapia</a></li>
              <li><a href="#services" className="hover:text-pink-600 transition-colors duration-300 flex items-center justify-center md:justify-start">Acupuntura</a></li>
            </ul>
          </div>
          
          <div className="space-y-6 text-center md:text-left">
            <h4 className="text-lg md:text-xl font-bold text-pink-600">Contato</h4>
            <div className="space-y-3 text-gray-600">
              <p className="flex items-center justify-center md:justify-start">📍 São Paulo, SP</p>
              <p className="flex items-center justify-center md:justify-start">📞 (11) 99999-9999</p>
              <p className="flex items-center justify-center md:justify-start">✉️ <EMAIL></p>
              <div className="pt-2 border-t border-gray-200">
                <p className="font-semibold text-gray-700 mb-2">Horários de Atendimento:</p>
                <p className="text-sm">Segunda a Sexta: 8h às 18h</p>
                <p className="text-sm">Sábado: 8h às 14h</p>
              </div>
            </div>
          </div>
        </div>
        
        <div className="border-t border-gray-200 mt-12 pt-8 text-center">
          <div className="flex flex-col sm:flex-row items-center justify-center space-y-3 sm:space-y-0 sm:space-x-3 text-gray-500">
            <span>© 2024 Mariah Blaj - Fisioterapeuta Pélvica.</span>
            <div className="flex items-center space-x-2">
              <span>Feito com</span>
              <Heart className="h-4 w-4 text-pink-500 fill-current" />
              <span>em São Paulo</span>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
